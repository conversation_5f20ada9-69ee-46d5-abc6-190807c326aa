import { disputedData, disputedLines } from "./disputedMapdata";

// Helper function to check if an array contains coordinate pairs
const isCoordinateArray = (arr: any): boolean => {
  return (
    Array.isArray(arr) &&
    arr.length >= 2 &&
    typeof arr[0] === "number" &&
    typeof arr[1] === "number"
  );
};

// Helper function to normalize coordinate arrays to proper format
const normalizeCoordinates = (coords: any): number[][] => {
  if (!Array.isArray(coords)) return [];

  // If it's already a coordinate pair [lon, lat], wrap it in an array
  if (isCoordinateArray(coords)) {
    return [coords];
  }

  // If it's an array of coordinate pairs [[lon, lat], [lon, lat], ...]
  if (coords.length > 0 && isCoordinateArray(coords[0])) {
    return coords;
  }

  // If it's nested deeper (like JammuOne), flatten one level
  if (
    coords.length > 0 &&
    Array.isArray(coords[0]) &&
    !isCoordinateArray(coords[0])
  ) {
    // This handles cases like [[[lon, lat], [lon, lat], ...]]
    const result: number[][] = [];
    for (const subArray of coords) {
      if (Array.isArray(subArray)) {
        const normalized = normalizeCoordinates(subArray);
        result.push(...normalized);
      }
    }
    return result;
  }

  return [];
};

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  const features: any = {
    type: "FeatureCollection",
    features: [],
  };

  Object.entries(disputedData).forEach(([name, regionData]: any) => {
    if (regionData.rings) {
      regionData.rings.forEach((ring: any, ringIndex: number) => {
        try {
          // Normalize the coordinates to handle nested structures
          const normalizedCoords = normalizeCoordinates(ring);

          // Skip rings with insufficient coordinates for a polygon (need at least 3)
          if (normalizedCoords.length < 3) {
            console.warn(
              `Skipping ${name} ring ${ringIndex}: insufficient coordinates (${normalizedCoords.length})`
            );
            return;
          }

          // Validate that all coordinates are valid numbers
          const validCoords = normalizedCoords.filter(
            coord =>
              Array.isArray(coord) &&
              coord.length >= 2 &&
              typeof coord[0] === "number" &&
              typeof coord[1] === "number" &&
              !isNaN(coord[0]) &&
              !isNaN(coord[1])
          );

          if (validCoords.length < 3) {
            console.warn(
              `Skipping ${name} ring ${ringIndex}: insufficient valid coordinates`
            );
            return;
          }

          // Ensure the polygon ring is closed
          const geographicRing = [...validCoords];
          if (
            geographicRing.length > 0 &&
            (geographicRing[0][0] !==
              geographicRing[geographicRing.length - 1][0] ||
              geographicRing[0][1] !==
                geographicRing[geographicRing.length - 1][1])
          ) {
            geographicRing.push(geographicRing[0]);
          }

          const feature = {
            type: "Feature",
            properties: { NAME: name, disputed: true },
            geometry: {
              type: "Polygon",
              coordinates: [geographicRing],
            },
          };

          features.features.push(feature);
        } catch (error) {
          console.error(`Error processing ${name} ring ${ringIndex}:`, error);
        }
      });
    }

    if (regionData.paths) {
      regionData.paths.forEach((path: any, pathIndex: number) => {
        try {
          // Normalize the coordinates to handle nested structures
          const normalizedCoords = normalizeCoordinates(path);

          // Skip paths with insufficient coordinates for a line (need at least 2)
          if (normalizedCoords.length < 2) {
            console.warn(
              `Skipping ${name} path ${pathIndex}: insufficient coordinates`
            );
            return;
          }

          // Validate that all coordinates are valid numbers
          const validCoords = normalizedCoords.filter(
            coord =>
              Array.isArray(coord) &&
              coord.length >= 2 &&
              typeof coord[0] === "number" &&
              typeof coord[1] === "number" &&
              !isNaN(coord[0]) &&
              !isNaN(coord[1])
          );

          if (validCoords.length < 2) {
            console.warn(
              `Skipping ${name} path ${pathIndex}: insufficient valid coordinates`
            );
            return;
          }

          const feature = {
            type: "Feature",
            properties: { NAME: name, disputed: true },
            geometry: {
              type: "LineString",
              coordinates: validCoords,
            },
          };

          features.features.push(feature);
        } catch (error) {
          console.error(`Error processing ${name} path ${pathIndex}:`, error);
        }
      });
    }
  });

  console.log(`Generated ${features.features.length} disputed features`);

  // Debug: Log the first few features to verify structure
  if (features.features.length > 0) {
    console.log(
      "Sample disputed feature:",
      JSON.stringify(features.features[0], null, 2)
    );
  }

  return features;
};
