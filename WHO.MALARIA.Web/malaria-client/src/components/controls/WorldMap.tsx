import { useState, useEffect } from "react";
import { geoEquirectangular, geoPath } from "d3-geo";
import { feature } from "topojson-client";
import { geoData } from "./worldMapdata";
import classNames from "classnames";
import classes from "./worldmap.module.scss";
import { CountryDataModel } from "../../models/GlobalDashboardModel";
import {
  WorldMapAssessmentApproachCountries,
  WorldMapAssessmentStatusCountries,
} from "../../models/Enums";
import { createDisputedFeatures } from "./createDisputedFeatures";

const projection = geoEquirectangular()
  .scale(160)
  .translate([800 / 2, 450 / 2]);

type WorldMapProps = {
  countries: Array<CountryDataModel>;
};
/** Renders world map component */
const WorldMap = (props: WorldMapProps) => {
  const [geographies, setGeographies] = useState([]);
  const { countries } = props;

  // set world map geographies
  useEffect(
    () => setGeographies(feature(geoData, geoData.objects.countries).features),
    []
  );

  // get assessment completion color
  const fill = (d: any): string => {
    switch (
      countries?.find((status: any) => status.iso === d.properties.ISO_A3)
        ?.status
    ) {
      case WorldMapAssessmentStatusCountries.DefaultStatus:
        return "#41bec6";
      case WorldMapAssessmentStatusCountries.InprogessAssessment:
      case WorldMapAssessmentApproachCountries.RapidAssessment:
        return "#948ce1";
      case WorldMapAssessmentStatusCountries.CompletedOneAssessment:
      case WorldMapAssessmentApproachCountries.TailoredAssessment:
        return "#e8bf53";
      case WorldMapAssessmentStatusCountries.MoreThanOneAssessment:
      case WorldMapAssessmentApproachCountries.ComprehensiveAssessment:
        return "#59d37b";
      default:
        return "#bec9d2";
    }
  };

  return (
    <div className={classNames(classes.mapWrapper)}>
      <svg viewBox='-100 -60 1000 500'>
        <g className='countries'>
          {geographies.map((d, i) => (
            <path
              key={`path-${i}`}
              d={geoPath().projection(projection)(d)}
              className='country'
              fill={fill(d)}
              stroke='#ddd'
              strokeWidth={1}
            />
          ))}
        </g>
        <g className='disputed-regions'>
          {createDisputedFeatures().features.map((d, i) => {
            try {
              const pathData = geoPath().projection(projection)(d);

              // Skip rendering if pathData is null or empty
              if (!pathData || pathData.length === 0) {
                console.warn(
                  `No path data for feature ${i} (${d.properties?.NAME || "unknown"})`
                );
                return null;
              }

              // Validate that the path data doesn't contain invalid values
              if (pathData.includes("NaN") || pathData.includes("Infinity")) {
                console.warn(
                  `Invalid path data for feature ${i} (${d.properties?.NAME || "unknown"}): contains NaN or Infinity`
                );
                return null;
              }

              return (
                <path
                  key={`disputed-${i}`}
                  d={pathData}
                  fill='#A9A9A9'
                  stroke='#ffffff'
                  strokeWidth={0.3}
                  fillOpacity={0.8}
                  style={{ pointerEvents: "none" }}
                />
              );
            } catch (error) {
              console.error(`Error rendering disputed feature ${i}:`, error);
              return null;
            }
          })}
        </g>
      </svg>
    </div>
  );
};

export default WorldMap;
