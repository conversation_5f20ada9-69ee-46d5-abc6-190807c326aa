import { useState, useEffect } from "react";
import { geoEquirectangular, geoPath, geoMercator } from "d3-geo";
import { feature } from "topojson-client";
import { geoData } from "../controls/worldMapdata";
import classNames from "classnames";
import { CountryDataModel } from "../../models/GlobalDashboardModel";
import {
  WorldMapAssessmentStatusCountries,
  WorldMapAssessmentApproachCountries,
  MetNotMetStatus,
} from "../../models/Enums";
import { useTranslation } from "react-i18next";
import { createDisputedFeatures } from "../controls/createDisputedFeatures";

const projection = geoEquirectangular()
  .scale(160)
  .translate([800 / 2, 450 / 2]);

type WorldMapProps = {
  countries: Array<CountryDataModel> | undefined;
};

/** Renders world map component for global objective dashboard */
const GlobalWorldMap = (props: WorldMapProps) => {
  const [geographies, setGeographies] = useState([]);
  const [disputedFeatures, setDisputedFeatures] = useState<any>(null);
  const { countries } = props;
  const { t } = useTranslation();

  // Set world map geographies
  useEffect(() => {
    setGeographies(feature(geoData, geoData.objects.countries).features);
  }, []);

  // Generate disputed features once
  useEffect(() => {
    try {
      const features = createDisputedFeatures();
      setDisputedFeatures(features);
    } catch (error) {
      console.error("Error generating disputed features:", error);
    }
  }, []);

  // get indicator met status completion color
  const fill = (d: any): string => {
    switch (
      countries?.find((status: any) => status.iso === d.properties.ISO_A3)
        ?.status
    ) {
      case MetNotMetStatus.Met:
        return "#6FEDA1"; //green
      case MetNotMetStatus.PartiallyMet:
        return "#F2E56F"; // yellow
      case MetNotMetStatus.NotMet:
        return "#FF9393"; // red
      case MetNotMetStatus.NotAssessed:
        return "#bec9d2"; // blueish gray
      default:
        return "#bec9d2"; // blueish gray
    }
  };

  return (
    <>
      <div className='app-dashboard mt-3'>
        <div className='worldMapWrapper'>
          <svg viewBox='-100 -60 1000 500'>
            <g className='countries'>
              {geographies.map((d, i) => (
                <path
                  key={`path-${i}`}
                  d={geoPath().projection(projection)(d)}
                  className='country'
                  fill={fill(d)}
                  stroke='#ddd'
                  strokeWidth={1}
                />
              ))}
            </g>

            {/* ✅ New: Disputed Regions Overlay */}
            <g className='disputed-regions'>
              {disputedFeatures?.features?.map((d: any, i: number) => {
                const pathData = geoPath().projection(projection)(d);

                // Skip rendering if pathData is null or empty
                if (!pathData) {
                  console.warn(
                    `No path data for feature ${i} (${d.properties.NAME})`
                  );
                  return null;
                }

                return (
                  <path
                    key={`disputed-${i}`}
                    d={pathData}
                    fill='#A9A9A9'
                    stroke='#ffffff'
                    strokeWidth={0.3}
                  />
                );
              })}
            </g>
          </svg>
        </div>

        <small className='d-flex mt-2 fst-italic'>
          * {t("Common.GeographicRegionsNote")}
        </small>
      </div>

      <div className='d-flex justify-content-center py-4'>
        <ul className='dashboard-legend'>
          <li>
            <span className='met'></span>
            {t("Common.Met")}
          </li>
          <li>
            <span className='partially-met'></span>
            {t("Common.PartiallyMet")}
          </li>
          <li>
            <span className='not-met'></span>
            {t("Common.NotMet")}
          </li>
          <li>
            <span className='not-assessed'></span>
            {t("Common.NotAssessed")}
          </li>
          <li>
            <span className='disputed'></span>
            {t("Common.disputed")}
          </li>
        </ul>
      </div>
    </>
  );
};

export default GlobalWorldMap;
